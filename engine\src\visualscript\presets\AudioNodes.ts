/**
 * 视觉脚本音频节点
 * 提供音频系统相关的节点
 */
import type { Entity } from '../../core/Entity';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Vector3 } from '../../math/Vector3';
import { AudioSystem } from '../../audio/AudioSystem';

/**
 * 播放音效节点 (208)
 * 播放指定的音效文件
 */
export class PlaySoundNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'audioUrl',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '音效文件URL'
    });

    this.addInput({
      name: 'volume',
      type: SocketType.DATA,
      dataType: 'number',
      direction: SocketDirection.INPUT,
      description: '音量 (0-1)',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'loop',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.INPUT,
      description: '是否循环播放',
      defaultValue: false
    });

    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      direction: SocketDirection.INPUT,
      description: '关联实体（可选）'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功播放'
    });

    this.addOutput({
      name: 'audioId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.OUTPUT,
      description: '音频实例ID'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const audioUrl = this.getInputValue('audioUrl') as string;
    const volume = this.getInputValue('volume') as number;
    const loop = this.getInputValue('loop') as boolean;
    const entity = this.getInputValue('entity') as Entity;

    // 检查输入值是否有效
    if (!audioUrl) {
      this.setOutputValue('success', false);
      this.setOutputValue('audioId', '');
      this.triggerFlow('flow');
      return false;
    }

    // 获取音频系统
    const audioSystem = this.context.world.getSystem(AudioSystem);
    if (!audioSystem) {
      console.error('音频系统未找到');
      this.setOutputValue('success', false);
      this.setOutputValue('audioId', '');
      this.triggerFlow('flow');
      return false;
    }

    // 播放音效
    try {
      // 生成音频ID
      const audioId = `sound_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // 播放音频
      const success = audioSystem.play(audioId, audioUrl, {
        volume: Math.max(0, Math.min(1, volume || 1.0)),
        loop: loop || false,
        type: 'sound'
      });

      // 设置输出值
      this.setOutputValue('success', success);
      this.setOutputValue('audioId', success ? audioId : '');

      // 触发输出流程
      this.triggerFlow('flow');

      return success;
    } catch (error) {
      console.error('播放音效失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);
      this.setOutputValue('audioId', '');

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 停止音效节点 (209)
 * 停止播放音效
 */
export class StopSoundNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'audioId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '音频实例ID（空则停止所有）'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功停止'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const audioId = this.getInputValue('audioId') as string;

    // 获取音频系统
    const audioSystem = this.context.world.getSystem(AudioSystem);
    if (!audioSystem) {
      console.error('音频系统未找到');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 停止音效
    try {
      let success = false;

      if (audioId) {
        // 停止指定音频
        success = audioSystem.stop(audioId);
      } else {
        // 停止所有音频
        audioSystem.stopAll();
        success = true;
      }

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发输出流程
      this.triggerFlow('flow');

      return success;
    } catch (error) {
      console.error('停止音效失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 暂停音效节点 (210)
 * 暂停音效播放
 */
export class PauseSoundNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'audioId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '音频实例ID（空则暂停所有）'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功暂停'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const audioId = this.getInputValue('audioId') as string;

    // 获取音频系统
    const audioSystem = this.context.world.getSystem(AudioSystem);
    if (!audioSystem) {
      console.error('音频系统未找到');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 暂停音效
    try {
      let success = false;

      if (audioId) {
        // 暂停指定音频
        success = audioSystem.pause(audioId);
      } else {
        // 暂停所有音频
        audioSystem.pauseAll();
        success = true;
      }

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发输出流程
      this.triggerFlow('flow');

      return success;
    } catch (error) {
      console.error('暂停音效失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 恢复音效节点
 * 恢复暂停的音效播放
 */
export class ResumeSoundNode extends FlowNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加输入数据插槽
    this.addInput({
      name: 'audioId',
      type: SocketType.DATA,
      dataType: 'string',
      direction: SocketDirection.INPUT,
      description: '音频实例ID（空则恢复所有）'
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '输出流程'
    });

    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      direction: SocketDirection.OUTPUT,
      description: '是否成功恢复'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const audioId = this.getInputValue('audioId') as string;

    // 获取音频系统
    const audioSystem = this.context.world.getSystem(AudioSystem);
    if (!audioSystem) {
      console.error('音频系统未找到');
      this.setOutputValue('success', false);
      this.triggerFlow('flow');
      return false;
    }

    // 恢复音效
    try {
      let success = false;

      if (audioId) {
        // 恢复指定音频
        success = audioSystem.resume(audioId);
      } else {
        // 恢复所有音频
        audioSystem.resumeAll();
        success = true;
      }

      // 设置输出值
      this.setOutputValue('success', success);

      // 触发输出流程
      this.triggerFlow('flow');

      return success;
    } catch (error) {
      console.error('恢复音效失败:', error);

      // 设置输出值
      this.setOutputValue('success', false);

      // 触发输出流程
      this.triggerFlow('flow');

      return false;
    }
  }
}

/**
 * 注册音频节点
 * @param registry 节点注册表
 */
export function registerAudioNodes(registry: NodeRegistry): void {
  // 注册播放音效节点 (208)
  registry.registerNodeType({
    type: 'audio/playSound',
    category: NodeCategory.AUDIO,
    constructor: PlaySoundNode,
    label: '播放音效',
    description: '播放指定的音效文件',
    icon: 'play-sound',
    color: '#FF9800',
    tags: ['audio', 'sound', 'play']
  });

  // 注册停止音效节点 (209)
  registry.registerNodeType({
    type: 'audio/stopSound',
    category: NodeCategory.AUDIO,
    constructor: StopSoundNode,
    label: '停止音效',
    description: '停止播放音效',
    icon: 'stop-sound',
    color: '#FF9800',
    tags: ['audio', 'sound', 'stop']
  });

  // 注册暂停音效节点 (210)
  registry.registerNodeType({
    type: 'audio/pauseSound',
    category: NodeCategory.AUDIO,
    constructor: PauseSoundNode,
    label: '暂停音效',
    description: '暂停音效播放',
    icon: 'pause-sound',
    color: '#FF9800',
    tags: ['audio', 'sound', 'pause']
  });

  // 注册恢复音效节点
  registry.registerNodeType({
    type: 'audio/resumeSound',
    category: NodeCategory.AUDIO,
    constructor: ResumeSoundNode,
    label: '恢复音效',
    description: '恢复暂停的音效播放',
    icon: 'resume-sound',
    color: '#FF9800',
    tags: ['audio', 'sound', 'resume']
  });
}
